from langgraph.graph import StateGraph

from src.classifier_service.non_court_docs.consts.case_claim import CaseClaimSubType
from src.classifier_service.non_court_docs.consts.financial import FinancialTypes
from src.classifier_service.non_court_docs.consts.general_classification import GeneralTypeClassification
from src.classifier_service.non_court_docs.consts.insurance import InsuranceSubType
from src.classifier_service.non_court_docs.consts.medical import HealthMedicalSubType



# TODO - test combining in one node
# def insurance_type_router(state) -> str:
#     insurance_type = state["classification_result"]
#
#     if insurance_type == InsuranceSubType.POLICY_DETAILS_AND_COVERAGE.value:
#         return "policy_details_and_coverage_type"
#     elif insurance_type == InsuranceSubType.CLAIMS_PROCESS_AND_COMMUNICATION.value:
#         return "claims_process_and_communication_type"
#     else:
#         return "unknown_insurance_type_handler"