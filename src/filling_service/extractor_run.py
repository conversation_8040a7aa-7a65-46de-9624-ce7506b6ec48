from src.classifier_service.common.utils.clean_storage_name import extract_blob_name
from src.filling_service.extractor_config import extractor
from src.filling_service.utils import build_schema_model
from src.shared.service_registry import doc_processor, blob_client
from src.shared.utils.logger_config import setup_logger

logger = setup_logger(__name__)


def execute_extraction_pipeline(doc_id):
    try:
        logger.info(f"---- STARTING FILLING SERVICE FOR DOCUMENT ID: {doc_id} ----")

        # Fetch document metadata
        email_info = doc_processor.get_document_info(doc_id)
        if not email_info:
            raise ValueError("Failed to retrieve document metadata.")

        document_location = email_info.get("storage_location")
        title = email_info.get("original_name", "")
        email_source = email_info.get("metadata", {}).get("source", "unknown")
        doc_type_id = email_info.get("document_type_id")
        template_id = email_info.get("company_data_source_id")

        logger.info(f"Document Location: {document_location}")
        logger.info(f"Email Source: {email_source}")
        logger.info(f"Document Title: {title}")

        # Get company ID and blob info
        company_id = doc_processor.get_company_id_by_document_id(doc_id)
        container_name = f"company-{company_id}"
        blob_name = extract_blob_name(document_location)

        logger.info(f"Company ID: {company_id}")
        logger.info(f"Container: {container_name}, Blob: {blob_name}")

        # Fetch preview content
        preview_bytes = blob_client.get_document_preview(container_name, blob_name, num_pages=5)
        if not preview_bytes:
            raise ValueError(f"Failed to fetch document from blob: {document_location}")

        logger.info(f"Fetched {len(preview_bytes)} bytes of preview content.")

        # Fetch target fields
        fields = doc_processor.get_fields_by_document_type_id(doc_type_id)
        logger.info(f"Fields to extract: {fields}")
        if not fields:
            logger.warning(f"No fields configured for document type ID {doc_type_id}. Skipping extraction.")
            return {}

        # Build dynamic schema
        schema_type = build_schema_model(fields)

        # Extract values using schema
        extracted_field_values = extractor.extract_field_values(preview_bytes, fields, schema_type)

        logger.info(f"Extraction complete for document ID {doc_id}")

        # Insert values into DB
        for field_name, field_value in extracted_field_values.items():
            if field_value:  # skip empty/null fields
                doc_processor.insert_field_value_for_document(doc_id, field_name, field_value)
                logger.info(f"Inserted field value for {field_name}: {field_value}")
            else:
                logger.info(f"Skipping empty field value for {field_name}")

        logger.info(f"Field values inserted for document ID {doc_id}")

        # Generate document name using extracted fields
        # TODO remove hardcoded doc_type and template_id ---> used for testing
        try:
            naming_template = doc_processor.get_naming_template_by_document_type_and_destination(document_type_id=101, company_data_destination_id=6)
            if naming_template and extracted_field_values:
                logger.info(f"Generating document name using template: {naming_template}")
                doc_name = extractor.generate_document_name(preview_bytes, extracted_field_values, naming_template)

                # Store the generated document name
                doc_processor.log_document_name(doc_id, doc_name)
                logger.info(f"Generated and stored document name: {doc_name}")
            else:
                if not naming_template:
                    logger.warning(f"No naming template found for document type ID {doc_type_id}")
                if not extracted_field_values:
                    logger.warning(f"No field values extracted for document ID {doc_id}")
        except Exception as naming_error:
            logger.error(f"Document naming failed for doc ID {doc_id}: {naming_error}", exc_info=True)
            # Continue execution even if naming fails

        return extracted_field_values

    except Exception as e:
        logger.error(f"Extraction pipeline failed for doc ID {doc_id}: {e}", exc_info=True)
        return {}

# result = execute_extraction_pipeline(268)
# print(result)
