"""
Test script to demonstrate the integrated naming service functionality.
"""
from src.filling_service.extractor_config import extractor
from src.filling_service.extractor_run import execute_extraction_pipeline
from src.shared.service_registry import doc_processor
from src.shared.utils.logger_config import setup_logger

logger = setup_logger(__name__)

def test_naming_service_separately(doc_id: int):
    """
    Test the naming service as a separate callable method after field extraction.
    """
    try:
        logger.info(f"Testing separate naming service call for document ID: {doc_id}")
        
        # First, get document info and extract fields (without naming)
        email_info = doc_processor.get_document_info(doc_id)
        if not email_info:
            raise ValueError("Failed to retrieve document metadata.")
        
        doc_type_id = email_info.get("document_type_id")
        
        # Get extracted field values (this would normally come from previous extraction)
        # For testing, let's simulate some extracted values
        sample_field_values = {
            "Date": "2024.01.15",
            "Name": "<PERSON>", 
            "Document Type": "Affidavit"
        }
        
        # Get naming template
        naming_template = doc_processor.get_naming_template_by_document_type_id(doc_type_id)
        logger.info(f"Using naming template: {naming_template}")
        
        # For testing, we'll use sample document bytes (in real scenario, this would be the actual document)
        sample_doc_bytes = b"Sample document content for testing"
        
        # Call the naming service separately
        generated_name = extractor.generate_document_name(
            document_bytes=sample_doc_bytes,
            field_values=sample_field_values,
            naming_template=naming_template
        )
        
        logger.info(f"Generated document name: {generated_name}")
        
        # Optionally store the name
        doc_processor.log_document_name(doc_id, generated_name)
        logger.info(f"Stored document name for document ID {doc_id}")
        
        return generated_name
        
    except Exception as e:
        logger.error(f"Naming service test failed: {e}", exc_info=True)
        return None

def test_full_pipeline_with_naming(doc_id: int):
    """
    Test the full extraction pipeline including automatic naming.
    """
    logger.info(f"Testing full pipeline with integrated naming for document ID: {doc_id}")
    
    # This will run field extraction AND naming automatically
    extracted_fields = execute_extraction_pipeline(doc_id)
    
    logger.info(f"Pipeline completed. Extracted fields: {extracted_fields}")
    return extracted_fields

if __name__ == "__main__":
    # Example usage:
    # test_doc_id = 268  # Replace with actual document ID
    # 
    # # Test 1: Use naming service separately after field extraction
    # generated_name = test_naming_service_separately(test_doc_id)
    # 
    # # Test 2: Use full integrated pipeline
    # extracted_data = test_full_pipeline_with_naming(test_doc_id)
    
    print("Test script ready. Uncomment the example usage section to run tests.")
